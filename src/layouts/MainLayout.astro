---
import Layout from './Layout.astro';
import Header from '../components/Header.astro';
import Footer from '../components/Footer.astro';
import "../styles/global.css";

export interface Props {
	title: string;
	description?: string;
}

const { title, description } = Astro.props;
---

<Layout title={title} description={description}>
	<div class="min-h-screen flex flex-col">
		<Header />
		<main class="flex-1">
			<slot />
		</main>
		<Footer />
	</div>
</Layout> 