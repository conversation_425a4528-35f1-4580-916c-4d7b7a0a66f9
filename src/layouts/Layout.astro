---
export interface Props {
	title: string;
	description?: string;
}

const { title, description = "青釭金融科技，以AI+大數據技術為驅動，為B2C行業用戶提供AI+智能決策和AI+智能生成技術服務" } = Astro.props;
---

<!doctype html>
<html lang="zh-TW">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<link rel="icon" type="image/svg+xml" href="/favicon.svg" />
		<meta name="generator" content={Astro.generator} />
		<meta name="description" content={description} />
		<title>{title}</title>
		
		<!-- Google Fonts -->
		<link rel="preconnect" href="https://fonts.googleapis.com">
		<link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
		<link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Noto+Sans+SC:wght@300;400;500;600;700&display=swap" rel="stylesheet">
	</head>
	<body class="font-sans text-gray-900 antialiased">
		<slot />
	</body>
</html>

<style>
	html {
		font-family: 'Noto Sans SC', 'Inter', system-ui, sans-serif;
		scroll-behavior: smooth;
	}
	
	body {
		margin: 0;
		line-height: 1.6;
		background-color: #ffffff;
	}
	
	/* Custom CSS Variables for color scheme */
	:root {
		--primary-blue: #0052CC;
		--secondary-gray: #4A4A4A;
		--accent-green: #00A86B;
		--highlight-orange: #F28C38;
		--text-dark: #1a1a1a;
		--text-light: #6b7280;
		--bg-light: #f8fafc;
	}
</style>
