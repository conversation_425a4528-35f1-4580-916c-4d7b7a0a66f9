---
// Language toggle component
---

<div class="relative">
	<button id="language-toggle" class="flex items-center space-x-1 px-3 py-2 text-sm font-medium text-gray-700 hover:text-gray-900 hover:bg-gray-100 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
		<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129"/>
		</svg>
		<span id="current-language">中文</span>
		<svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
			<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
		</svg>
	</button>
	
	<div id="language-menu" class="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg opacity-0 invisible transition-all duration-200 z-50">
		<div class="py-1">
			<button data-lang="zh" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">中文</button>
			<button data-lang="en" class="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-50">English</button>
		</div>
	</div>
</div>

<script>
	document.addEventListener('DOMContentLoaded', function() {
		const toggleButton = document.getElementById('language-toggle');
		const languageMenu = document.getElementById('language-menu');
		const currentLanguage = document.getElementById('current-language');
		const languageOptions = document.querySelectorAll('[data-lang]');
		
		// Toggle menu visibility
		toggleButton?.addEventListener('click', function() {
			const isVisible = !languageMenu?.classList.contains('invisible');
			if (isVisible) {
				languageMenu?.classList.add('opacity-0', 'invisible');
			} else {
				languageMenu?.classList.remove('opacity-0', 'invisible');
			}
		});
		
		// Handle language selection
		languageOptions.forEach(option => {
			option.addEventListener('click', function(event) {
				const button = event.currentTarget;
				const lang = button.getAttribute('data-lang');
				if (currentLanguage) {
					if (lang === 'en') {
						currentLanguage.textContent = 'English';
					} else {
						currentLanguage.textContent = '中文';
					}
				}
				languageMenu?.classList.add('opacity-0', 'invisible');
			});
		});
		
		// Close menu when clicking outside
		document.addEventListener('click', function(event) {
			const target = event.target;
			if (target && toggleButton && languageMenu) {
				if (!toggleButton.contains(target) && !languageMenu.contains(target)) {
					languageMenu.classList.add('opacity-0', 'invisible');
				}
			}
		});
	});
</script> 