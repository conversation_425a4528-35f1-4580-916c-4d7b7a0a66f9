---
export interface Props {
	title: string;
	description: string;
	color?: "blue" | "green" | "purple" | "orange";
}

const { title, description, color = "blue" } = Astro.props;

const colorClasses = {
	blue: "bg-blue-600/70 border-blue-500",
	green: "bg-green-600/70 border-green-500", 
	purple: "bg-purple-600/70 border-purple-500",
	orange: "bg-orange-600/70 border-orange-500"
};
---

<div class="text-center my-20">
	<div class={`inline-block ${colorClasses[color]} text-white px-3 py-1 rounded-full font-semibold text-sm border`}>
		{title}
	</div>
	<p class="text-gray-600 mt-3 text-lg">{description}</p>
</div> 